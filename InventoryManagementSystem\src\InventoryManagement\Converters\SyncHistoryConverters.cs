using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using InventoryManagement.Models;

namespace InventoryManagement.Converters
{
    public class SyncSessionStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SyncSessionStatus)
            {
                SyncSessionStatus status = (SyncSessionStatus)value;
                if (status == SyncSessionStatus.Success)
                    return Brushes.Green;
                if (status == SyncSessionStatus.Failed)
                    return Brushes.Red;
                if (status == SyncSessionStatus.Canceled)
                    return Brushes.Orange;
                if (status == SyncSessionStatus.InProgress)
                    return Brushes.Blue;
            }
            return Brushes.Gray;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    public class ResultColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SyncOperationResult)
            {
                SyncOperationResult result = (SyncOperationResult)value;
                if (result == SyncOperationResult.Success)
                    return Brushes.Green;
                if (result == SyncOperationResult.Failed)
                    return Brushes.Red;
                if (result == SyncOperationResult.Conflict)
                    return Brushes.Orange;
                if (result == SyncOperationResult.PartialSuccess)
                    return Brushes.Yellow;
            }
            return Brushes.Gray;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    

    
    public class HasConflictConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Guid?)
            {
                Guid? guidValue = (Guid?)value;
                if (guidValue.HasValue)
                {
                    return Visibility.Visible;
                }
                else
                {
                    return Visibility.Collapsed;
                }
            }
            return Visibility.Collapsed;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
