using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using InventoryManagement.Services.Interfaces;
using InventoryManagement.Models.Reports;
using OfficeOpenXml;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace InventoryManagement.Services
{
    /// <summary>
    /// Implementation of report exporter for various formats
    /// </summary>
    public class ReportExporter : IReportExporter
    {
        private readonly ILogger<ReportExporter> _logger;

        public ReportExporter(ILogger<ReportExporter> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Configure QuestPDF license
            QuestPDF.Settings.License = LicenseType.Community;
        }

        public async Task<byte[]> ExportReportAsync(BaseReport report, ExportFormat format)
        {
            try
            {
                _logger.LogInformation("Exporting report '{Title}' to {Format} format", report.Title, format);
                
                return format switch
                {
                    ExportFormat.PDF => await ExportToPdfAsync(report),
                    ExportFormat.Excel => await ExportToExcelAsync(report),
                    ExportFormat.CSV => await ExportToCsvAsync(report),
                    _ => throw new ArgumentException($"Unsupported export format: {format}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to {Format}", format);
                throw;
            }
        }

        public async Task<byte[]> ExportDataAsync(DataTable data, ExportFormat format, string title = null, ExportOptions options = null)
        {
            var report = new BaseReport
            {
                Title = title ?? "Data Export",
                GeneratedAt = DateTime.Now,
                Data = data
            };

            return await ExportReportAsync(report, format);
        }

        public async Task<bool> ExportToFileAsync(BaseReport report, ExportFormat format, string filePath)
        {
            try
            {
                var data = await ExportReportAsync(report, format);
                await File.WriteAllBytesAsync(filePath, data);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to file: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<bool> ExportDataToFileAsync(DataTable data, ExportFormat format, string filePath, string title = null, ExportOptions options = null)
        {
            try
            {
                var reportData = await ExportDataAsync(data, format, title, options);
                await File.WriteAllBytesAsync(filePath, reportData);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data to file: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<bool> ExportToStreamAsync(BaseReport report, ExportFormat format, Stream stream)
        {
            try
            {
                var data = await ExportReportAsync(report, format);
                await stream.WriteAsync(data, 0, data.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report to stream");
                return false;
            }
        }

        public async Task<bool> ExportDataToStreamAsync(DataTable data, ExportFormat format, Stream stream, string title = null, ExportOptions options = null)
        {
            try
            {
                var reportData = await ExportDataAsync(data, format, title, options);
                await stream.WriteAsync(reportData, 0, reportData.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data to stream");
                return false;
            }
        }

        public string GetMimeType(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.PDF => "application/pdf",
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.CSV => "text/csv",
                _ => "application/octet-stream"
            };
        }

        public string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.PDF => ".pdf",
                ExportFormat.Excel => ".xlsx",
                ExportFormat.CSV => ".csv",
                _ => ".bin"
            };
        }

        public List<ExportFormat> GetSupportedFormats()
        {
            return new List<ExportFormat> { ExportFormat.PDF, ExportFormat.Excel, ExportFormat.CSV };
        }

        public bool IsFormatSupported(ExportFormat format)
        {
            return GetSupportedFormats().Contains(format);
        }

        public async Task<byte[]> ExportMultipleReportsAsync(List<BaseReport> reports, ExportFormat format, string title = null)
        {
            if (format == ExportFormat.PDF)
            {
                return await ExportMultipleReportsToPdfAsync(reports, title);
            }
            
            throw new NotSupportedException($"Multiple report export is not supported for format: {format}");
        }

        public async Task<byte[]> ExportWithFormattingAsync(BaseReport report, ExportFormat format, ReportFormatting formatting)
        {
            // For now, use the standard export method
            // In a full implementation, this would apply custom formatting
            return await ExportReportAsync(report, format);
        }

        public async Task<byte[]> ExportWithColumnFormattingAsync(DataTable data, ExportFormat format, Dictionary<string, ColumnFormatting> columnFormatting, string title = null)
        {
            // For now, use the standard export method
            // In a full implementation, this would apply column-specific formatting
            return await ExportDataAsync(data, format, title);
        }

        public async Task<ReportPreview> GeneratePreviewAsync(BaseReport report, ExportFormat format, int maxRows = 100)
        {
            var previewData = report.Data?.Clone() as DataTable;
            
            if (previewData != null && previewData.Rows.Count > maxRows)
            {
                // Remove excess rows for preview
                for (int i = previewData.Rows.Count - 1; i >= maxRows; i--)
                {
                    previewData.Rows.RemoveAt(i);
                }
            }

            var estimatedSize = await EstimateFileSizeAsync(report, format);

            return new ReportPreview
            {
                PreviewData = previewData,
                TotalRows = report.Data?.Rows.Count ?? 0,
                ColumnCount = report.Data?.Columns.Count ?? 0,
                EstimatedFileSize = estimatedSize,
                Format = format,
                Warnings = new List<string>()
            };
        }

        public async Task<long> EstimateFileSizeAsync(BaseReport report, ExportFormat format)
        {
            if (report.Data == null) return 0;

            // Simple estimation based on data size and format
            var dataSize = report.Data.Rows.Count * report.Data.Columns.Count * 20; // Average 20 bytes per cell

            return format switch
            {
                ExportFormat.CSV => dataSize,
                ExportFormat.Excel => dataSize * 2, // Excel has more overhead
                ExportFormat.PDF => dataSize * 3, // PDF has the most overhead
                _ => dataSize
            };
        }

        public async Task<byte[]> ExportWithPasswordAsync(BaseReport report, ExportFormat format, string password)
        {
            if (format == ExportFormat.Excel)
            {
                return await ExportToPasswordProtectedExcelAsync(report, password);
            }
            
            throw new NotSupportedException($"Password protection is not supported for format: {format}");
        }

        private async Task<byte[]> ExportToPdfAsync(BaseReport report)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.PageColor(Colors.White);
                        page.DefaultTextStyle(x => x.FontSize(10));

                        page.Header()
                            .Text(report.Title)
                            .SemiBold().FontSize(16).FontColor(Colors.Blue.Medium);

                        page.Content()
                            .PaddingVertical(1, Unit.Centimetre)
                            .Column(x =>
                            {
                                x.Spacing(20);

                                if (report.Data != null)
                                {
                                    x.Item().Table(table =>
                                    {
                                        // Define columns
                                        table.ColumnsDefinition(columns =>
                                        {
                                            for (int i = 0; i < report.Data.Columns.Count; i++)
                                            {
                                                columns.RelativeColumn();
                                            }
                                        });

                                        // Header
                                        table.Header(header =>
                                        {
                                            foreach (DataColumn column in report.Data.Columns)
                                            {
                                                header.Cell().Element(CellStyle).Text(column.ColumnName).SemiBold();
                                            }
                                        });

                                        // Data rows
                                        foreach (DataRow row in report.Data.Rows)
                                        {
                                            foreach (var item in row.ItemArray)
                                            {
                                                table.Cell().Element(CellStyle).Text(item?.ToString() ?? "");
                                            }
                                        }
                                    });
                                }
                            });

                        page.Footer()
                            .AlignCenter()
                            .Text(x =>
                            {
                                x.Span("Generated on ");
                                x.Span(report.GeneratedAt.ToString("yyyy-MM-dd HH:mm")).SemiBold();
                            });
                    });
                });

                return document.GeneratePdf();
            });
        }

        private static IContainer CellStyle(IContainer container)
        {
            return container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
        }

        private async Task<byte[]> ExportToExcelAsync(BaseReport report)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add(report.Title ?? "Report");

                if (report.Data != null)
                {
                    // Add headers
                    for (int col = 0; col < report.Data.Columns.Count; col++)
                    {
                        worksheet.Cells[1, col + 1].Value = report.Data.Columns[col].ColumnName;
                        worksheet.Cells[1, col + 1].Style.Font.Bold = true;
                    }

                    // Add data
                    for (int row = 0; row < report.Data.Rows.Count; row++)
                    {
                        for (int col = 0; col < report.Data.Columns.Count; col++)
                        {
                            worksheet.Cells[row + 2, col + 1].Value = report.Data.Rows[row][col];
                        }
                    }

                    // Auto-fit columns
                    worksheet.Cells.AutoFitColumns();
                }

                return package.GetAsByteArray();
            });
        }

        private async Task<byte[]> ExportToCsvAsync(BaseReport report)
        {
            return await Task.Run(() =>
            {
                var csv = new StringBuilder();

                if (report.Data != null)
                {
                    // Add headers
                    var headers = report.Data.Columns.Cast<DataColumn>().Select(column => column.ColumnName);
                    csv.AppendLine(string.Join(",", headers.Select(EscapeCsvField)));

                    // Add data
                    foreach (DataRow row in report.Data.Rows)
                    {
                        var fields = row.ItemArray.Select(field => EscapeCsvField(field?.ToString() ?? ""));
                        csv.AppendLine(string.Join(",", fields));
                    }
                }

                return Encoding.UTF8.GetBytes(csv.ToString());
            });
        }

        private static string EscapeCsvField(string field)
        {
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }
            return field;
        }

        private async Task<byte[]> ExportMultipleReportsToPdfAsync(List<BaseReport> reports, string title)
        {
            return await Task.Run(() =>
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);
                        page.PageColor(Colors.White);
                        page.DefaultTextStyle(x => x.FontSize(10));

                        page.Header()
                            .Text(title ?? "Combined Reports")
                            .SemiBold().FontSize(16).FontColor(Colors.Blue.Medium);

                        page.Content()
                            .PaddingVertical(1, Unit.Centimetre)
                            .Column(x =>
                            {
                                x.Spacing(20);

                                foreach (var report in reports)
                                {
                                    x.Item().Text(report.Title).SemiBold().FontSize(14);
                                    
                                    if (report.Data != null)
                                    {
                                        x.Item().Table(table =>
                                        {
                                            // Define columns
                                            table.ColumnsDefinition(columns =>
                                            {
                                                for (int i = 0; i < report.Data.Columns.Count; i++)
                                                {
                                                    columns.RelativeColumn();
                                                }
                                            });

                                            // Header
                                            table.Header(header =>
                                            {
                                                foreach (DataColumn column in report.Data.Columns)
                                                {
                                                    header.Cell().Element(CellStyle).Text(column.ColumnName).SemiBold();
                                                }
                                            });

                                            // Data rows (limit to first 50 rows for combined reports)
                                            var rowCount = Math.Min(50, report.Data.Rows.Count);
                                            for (int i = 0; i < rowCount; i++)
                                            {
                                                var row = report.Data.Rows[i];
                                                foreach (var item in row.ItemArray)
                                                {
                                                    table.Cell().Element(CellStyle).Text(item?.ToString() ?? "");
                                                }
                                            }
                                        });
                                    }
                                    
                                    x.Item().PageBreak();
                                }
                            });

                        page.Footer()
                            .AlignCenter()
                            .Text(x =>
                            {
                                x.Span("Generated on ");
                                x.Span(DateTime.Now.ToString("yyyy-MM-dd HH:mm")).SemiBold();
                            });
                    });
                });

                return document.GeneratePdf();
            });
        }

        private async Task<byte[]> ExportToPasswordProtectedExcelAsync(BaseReport report, string password)
        {
            return await Task.Run(() =>
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add(report.Title ?? "Report");

                if (report.Data != null)
                {
                    // Add headers
                    for (int col = 0; col < report.Data.Columns.Count; col++)
                    {
                        worksheet.Cells[1, col + 1].Value = report.Data.Columns[col].ColumnName;
                        worksheet.Cells[1, col + 1].Style.Font.Bold = true;
                    }

                    // Add data
                    for (int row = 0; row < report.Data.Rows.Count; row++)
                    {
                        for (int col = 0; col < report.Data.Columns.Count; col++)
                        {
                            worksheet.Cells[row + 2, col + 1].Value = report.Data.Rows[row][col];
                        }
                    }

                    // Auto-fit columns
                    worksheet.Cells.AutoFitColumns();
                }

                // Set password protection
                package.Workbook.Protection.SetPassword(password);

                return package.GetAsByteArray();
            });
        }
    }
}
