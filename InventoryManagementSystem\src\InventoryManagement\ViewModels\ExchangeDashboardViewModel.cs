using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using InventoryManagement.Commands;
using InventoryManagement.Models;
using InventoryManagement.DataAccess;
using InventoryManagement.Services;
using InventoryManagement.Services.Data;
using InventoryManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using IAuditService = InventoryManagement.Services.IAuditService;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Exchange section of the main dashboard
    /// </summary>
    public class ExchangeDashboardViewModel : ViewModelBase
    {
        private readonly IDataContext _dataContext;
        private readonly IAuditService _auditService;
        private readonly IUserService _userService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private ObservableCollection<ExchangeViewModel> _exchanges;
        private ICollectionView _filteredExchanges;
        private string _searchText;
        private DateTime _startDate;
        private DateTime _endDate;
        private string _selectedStatus;
        private bool _isLoading;
        private int _totalExchanges;
        private int _pendingExchanges;
        private int _completedExchanges;
        private decimal _financialImpact;
        
        public ExchangeDashboardViewModel(
            IDataContext dataContext,
            IAuditService auditService,
            IUserService userService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _dataContext = dataContext ?? throw new ArgumentNullException(nameof(dataContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize commands
            SearchCommand = new RelayCommand(param => ApplyFilters());
            ApplyDateRangeCommand = new RelayCommand(param => ApplyFilters());
            NewExchangeCommand = new RelayCommand(param => OpenNewExchangeDialog());
            RefreshCommand = new RelayCommand(async param => await LoadExchangesAsync());
            
            // Initialize collections
            Exchanges = new ObservableCollection<ExchangeViewModel>();
            
            // Set default date range to last 30 days
            EndDate = DateTime.Now;
            StartDate = DateTime.Now.AddDays(-30);
            
            // Initial load
            _ = InitializeAsync();
        }
        
        #region Properties
        
        public ObservableCollection<ExchangeViewModel> Exchanges
        {
            get => _exchanges;
            set => SetProperty(ref _exchanges, value);
        }
        
        public ICollectionView FilteredExchanges
        {
            get => _filteredExchanges;
            set => SetProperty(ref _filteredExchanges, value);
        }
        
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }
        
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    OnPropertyChanged(nameof(DateRangeText));
                }
            }
        }
        
        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    OnPropertyChanged(nameof(DateRangeText));
                }
            }
        }
        
        public string DateRangeText => $"{StartDate:MM/dd/yyyy} - {EndDate:MM/dd/yyyy}";
        
        public string SelectedStatus
        {
            get => _selectedStatus;
            set
            {
                if (SetProperty(ref _selectedStatus, value))
                {
                    ApplyFilters();
                }
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }
        
        public int TotalExchanges
        {
            get => _totalExchanges;
            set => SetProperty(ref _totalExchanges, value);
        }
        
        public int PendingExchanges
        {
            get => _pendingExchanges;
            set => SetProperty(ref _pendingExchanges, value);
        }
        
        public int CompletedExchanges
        {
            get => _completedExchanges;
            set => SetProperty(ref _completedExchanges, value);
        }
        
        public decimal FinancialImpact
        {
            get => _financialImpact;
            set => SetProperty(ref _financialImpact, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand SearchCommand { get; }
        public ICommand ApplyDateRangeCommand { get; }
        public ICommand NewExchangeCommand { get; }
        public ICommand RefreshCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                await LoadExchangesAsync();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error initializing Exchange Dashboard");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadExchangesAsync()
        {
            try
            {
                IsLoading = true;
                
                // Query the database for item exchanges within the date range
                var exchanges = await _dataContext.ItemExchanges
                    .Where(e => e.ExchangeDate >= StartDate && e.ExchangeDate <= EndDate)
                    .Include(e => e.ReturnedItem)
                    .Include(e => e.ExchangeItem)
                    .Include(e => e.Customer)
                    .Include(e => e.ProcessedByUser)
                    .OrderByDescending(e => e.ExchangeDate)
                    .ToListAsync();
                
                // Clear existing exchanges
                Exchanges.Clear();
                
                // Calculate statistics
                TotalExchanges = exchanges.Count;
                PendingExchanges = exchanges.Count(e => e.Status == ExchangeStatus.Pending);
                CompletedExchanges = exchanges.Count(e => e.Status == ExchangeStatus.Completed);
                
                // Calculate financial impact (value difference between returned and exchanged items)
                FinancialImpact = exchanges.Sum(e => 
                    (e.ExchangeItem?.SellingPrice ?? 0) - (e.ReturnedItem?.SellingPrice ?? 0));
                
                // Add exchanges to collection
                foreach (var exchange in exchanges)
                {
                    Exchanges.Add(new ExchangeViewModel(
                        exchange, 
                        _auditService,
                        _exceptionHandler));
                }
                
                // Set up filtering
                FilteredExchanges = CollectionViewSource.GetDefaultView(Exchanges);
                FilteredExchanges.Filter = ExchangeFilter;
                
                // Log audit trail for viewing exchanges
                await _auditService.LogActionAsync(
                    "Exchanges", 
                    "View", 
                    $"Viewed {Exchanges.Count} exchanges for period {StartDate:MM/dd/yyyy} to {EndDate:MM/dd/yyyy}", 
                    null);
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading exchanges");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private bool ExchangeFilter(object item)
        {
            if (item is not ExchangeViewModel exchange)
                return false;
            
            // Apply search text filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                bool matchesSearch = exchange.ExchangeId.ToString().Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    exchange.CustomerName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    exchange.ReturnedItemName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    exchange.ExchangeItemName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                                    exchange.ProcessedByName.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
                
                if (!matchesSearch)
                    return false;
            }
            
            // Apply status filter
            if (!string.IsNullOrWhiteSpace(SelectedStatus) && SelectedStatus != "All Statuses")
            {
                if (exchange.Status != SelectedStatus)
                    return false;
            }
            
            // Exchange date is already filtered in the database query
            
            return true;
        }
        
        private void ApplyFilters()
        {
            FilteredExchanges?.Refresh();
        }
        
        private void OpenNewExchangeDialog()
        {
            // Implementation would open a dialog for creating a new exchange
            // This would typically use a dialog service
        }
        
        #endregion
    }
    
    /// <summary>
    /// ViewModel for an individual item exchange in the UI
    /// </summary>
    public class ExchangeViewModel : ViewModelBase
    {
        private readonly ItemExchange _exchange;
        private readonly IAuditService _auditService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        public ExchangeViewModel(
            ItemExchange exchange,
            IAuditService auditService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _exchange = exchange ?? throw new ArgumentNullException(nameof(exchange));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize commands
            ViewDetailsCommand = new RelayCommand(param => ViewDetails());
            ProcessExchangeCommand = new RelayCommand(param => ProcessExchange(), param => CanProcessExchange());
        }
        
        #region Properties
        
        public int ExchangeId => _exchange.Id;
        public DateTime ExchangeDate => _exchange.ExchangeDate;
        
        public string CustomerName => _exchange.Customer?.FullName ?? "Walk-in Customer";
        public string CustomerPhone => _exchange.Customer?.PhoneNumber ?? string.Empty;
        
        public string ReturnedItemName => _exchange.ReturnedItem?.Name ?? "Unknown Item";
        public string ReturnedItemSKU => _exchange.ReturnedItem?.SKU ?? string.Empty;
        public decimal ReturnedItemPrice => _exchange.ReturnedItem?.SellingPrice ?? 0;
        
        public string ExchangeItemName => _exchange.ExchangeItem?.Name ?? "Unknown Item";
        public string ExchangeItemSKU => _exchange.ExchangeItem?.SKU ?? string.Empty;
        public decimal ExchangeItemPrice => _exchange.ExchangeItem?.SellingPrice ?? 0;
        
        public decimal PriceDifference => ExchangeItemPrice - ReturnedItemPrice;
        public bool IsPriceDifferencePositive => PriceDifference > 0;
        
        public string ProcessedByName => _exchange.ProcessedByUser?.FullName ?? string.Empty;
        public string Reason => _exchange.Reason;
        public string Notes => _exchange.Notes;
        
        public string Status => _exchange.Status.ToString();
        
        public SolidColorBrush StatusColor
        {
            get
            {
                return _exchange.Status switch
                {
                    ExchangeStatus.Pending => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")), // Orange
                    ExchangeStatus.Completed => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")), // Green
                    ExchangeStatus.Cancelled => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336")), // Red
                    _ => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#757575")), // Gray
                };
            }
        }
        
        #endregion
        
        #region Commands
        
        public ICommand ViewDetailsCommand { get; }
        public ICommand ProcessExchangeCommand { get; }
        
        #endregion
        
        #region Methods
        
        private void ViewDetails()
        {
            // Implementation would navigate to exchange details view
            // This would typically use a navigation service
        }
        
        private bool CanProcessExchange()
        {
            return _exchange.Status == ExchangeStatus.Pending;
        }
        
        private async void ProcessExchange()
        {
            try
            {
                // In a real implementation, we would update the exchange in the database
                // For this example, we just log the action
                
                // Mark exchange as completed
                _exchange.Status = ExchangeStatus.Completed;
                _exchange.CompletionDate = DateTime.Now;
                
                // Log the action
                await _auditService.LogActionAsync(
                    "Exchanges",
                    "Process",
                    $"Processed exchange {_exchange.Id} from {ReturnedItemName} to {ExchangeItemName}",
                    _exchange.Id.ToString());
                
                // Notify of property change
                OnPropertyChanged(nameof(Status));
                OnPropertyChanged(nameof(StatusColor));
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error processing exchange");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Enum for exchange status
    /// </summary>
    public enum ExchangeStatus
    {
        Pending,
        Completed,
        Cancelled
    }
}
