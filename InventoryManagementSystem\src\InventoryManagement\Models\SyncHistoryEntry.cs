using System;
using System.Collections.Generic;

namespace InventoryManagement.Models
{
    /// <summary>
    /// Represents a synchronization session history entry
    /// </summary>
    public class SyncHistoryEntry
    {
        /// <summary>
        /// Unique identifier for the sync session
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// When the sync session started
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// When the sync session completed (null if still in progress)
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// User who initiated the sync
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// User name who initiated the sync (for display purposes)
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// Device identifier where the sync was initiated
        /// </summary>
        public string DeviceId { get; set; }
        
        /// <summary>
        /// Terminal identifier where the sync was initiated
        /// </summary>
        public string TerminalId { get; set; }
        
        /// <summary>
        /// Status of the sync session
        /// </summary>
        public SyncSessionStatus Status { get; set; } = SyncSessionStatus.InProgress;
        
        /// <summary>
        /// Total number of operations in this sync session
        /// </summary>
        public int TotalOperations { get; set; }
        
        /// <summary>
        /// Number of operations successfully processed
        /// </summary>
        public int SuccessfulOperations { get; set; }
        
        /// <summary>
        /// Number of operations that failed
        /// </summary>
        public int FailedOperations { get; set; }
        
        /// <summary>
        /// Number of conflicts encountered
        /// </summary>
        public int ConflictCount { get; set; }
        
        /// <summary>
        /// Number of conflicts automatically resolved
        /// </summary>
        public int AutoResolvedConflicts { get; set; }
        
        /// <summary>
        /// Number of conflicts manually resolved
        /// </summary>
        public int ManuallyResolvedConflicts { get; set; }
        
        /// <summary>
        /// Whether the session was cancelled
        /// </summary>
        public bool WasCancelled { get; set; }
        
        /// <summary>
        /// Additional notes about the sync session
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// Detailed operations processed in this session
        /// </summary>
        public List<SyncOperationEntry> Operations { get; set; } = new List<SyncOperationEntry>();
        
        /// <summary>
        /// Duration of the sync session in seconds
        /// </summary>
        public double DurationSeconds
        {
            get
            {
                if (EndTime.HasValue)
                {
                    return (EndTime.Value - StartTime).TotalSeconds;
                }
                
                return (DateTime.Now - StartTime).TotalSeconds;
            }
        }
        
        /// <summary>
        /// Success rate as a percentage (0-100)
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (TotalOperations == 0)
                {
                    return 0;
                }
                
                return (double)SuccessfulOperations / TotalOperations * 100;
            }
        }
    }
    

    
    /// <summary>
    /// Represents a single operation within a sync session
    /// </summary>
    public class SyncOperationEntry
    {
        /// <summary>
        /// Unique identifier for the operation entry
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>
        /// ID of the sync session this operation belongs to
        /// </summary>
        public Guid SyncSessionId { get; set; }
        
        /// <summary>
        /// ID of the offline operation being processed
        /// </summary>
        public Guid OfflineOperationId { get; set; }
        
        /// <summary>
        /// Type of entity being processed
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// ID of the entity being processed
        /// </summary>
        public string EntityId { get; set; }
        
        /// <summary>
        /// Type of operation being performed
        /// </summary>
        public string OperationType { get; set; }
        
        /// <summary>
        /// When the operation processing started
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// When the operation processing completed
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// Result of the operation
        /// </summary>
        public SyncOperationResult Result { get; set; }
        
        /// <summary>
        /// ID of any conflict detected during processing
        /// </summary>
        public Guid? ConflictId { get; set; }
        
        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// Duration of the operation in milliseconds
        /// </summary>
        public double DurationMs
        {
            get
            {
                if (EndTime.HasValue)
                {
                    return (EndTime.Value - StartTime).TotalMilliseconds;
                }
                
                return (DateTime.Now - StartTime).TotalMilliseconds;
            }
        }
    }
}
