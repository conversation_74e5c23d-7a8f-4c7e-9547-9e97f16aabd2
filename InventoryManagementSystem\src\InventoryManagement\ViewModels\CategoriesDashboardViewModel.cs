using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Input;
using InventoryManagement.Commands;
using InventoryManagement.Models;
using InventoryManagement.DataAccess;
using InventoryManagement.Services;
using InventoryManagement.Infrastructure.Data;
using InventoryManagement.Services.Data;
using IAuditService = InventoryManagement.Services.IAuditService;
using Microsoft.EntityFrameworkCore;

namespace InventoryManagement.ViewModels
{
    /// <summary>
    /// ViewModel for the Categories section of the main dashboard
    /// </summary>
    public class CategoriesDashboardViewModel : ViewModelBase
    {
        private readonly IDataContext _dataContext;
        private readonly IAuditService _auditService;
        private readonly ICacheService _cacheService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        
        private ObservableCollection<CategoryViewModel> _categories;
        private ICollectionView _filteredCategories;
        private string _searchText;
        private bool _isLoading;
        private CategoryViewModel _selectedCategory;
        private int _totalCategories;
        private int _totalSubcategories;
        private int _itemsWithoutCategory;
        
        public CategoriesDashboardViewModel(
            IDataContext dataContext,
            IAuditService auditService,
            ICacheService cacheService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _dataContext = dataContext ?? throw new ArgumentNullException(nameof(dataContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize commands
            SearchCommand = new RelayCommand(param => ApplyFilters());
            AddCategoryCommand = new RelayCommand(param => OpenAddCategoryDialog());
            RefreshCommand = new RelayCommand(async param => await LoadCategoriesAsync());
            
            // Initialize collections
            Categories = new ObservableCollection<CategoryViewModel>();
            
            // Initial load
            _ = InitializeAsync();
        }
        
        #region Properties
        
        public ObservableCollection<CategoryViewModel> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }
        
        public ICollectionView FilteredCategories
        {
            get => _filteredCategories;
            set => SetProperty(ref _filteredCategories, value);
        }
        
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }
        
        public CategoryViewModel SelectedCategory
        {
            get => _selectedCategory;
            set => SetProperty(ref _selectedCategory, value);
        }
        
        public int TotalCategories
        {
            get => _totalCategories;
            set => SetProperty(ref _totalCategories, value);
        }
        
        public int TotalSubcategories
        {
            get => _totalSubcategories;
            set => SetProperty(ref _totalSubcategories, value);
        }
        
        public int ItemsWithoutCategory
        {
            get => _itemsWithoutCategory;
            set => SetProperty(ref _itemsWithoutCategory, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand SearchCommand { get; }
        public ICommand AddCategoryCommand { get; }
        public ICommand RefreshCommand { get; }
        
        #endregion
        
        #region Methods
        
        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                await LoadCategoriesAsync();
                await LoadStatisticsAsync();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error initializing Categories Dashboard");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadCategoriesAsync()
        {
            try
            {
                IsLoading = true;
                
                // Check if categories are in cache
                var cachedCategories = _cacheService.Get<Dictionary<string, HashSet<string>>>("CategoryHierarchy");
                
                if (cachedCategories == null)
                {
                    // Not in cache, load from database
                    var items = await _dataContext.Items
                        .Where(i => !i.IsDeleted)
                        .Select(i => new { i.Category, i.SubCategory })
                        .ToListAsync();
                    
                    // Build category hierarchy
                    cachedCategories = new Dictionary<string, HashSet<string>>();
                    
                    foreach (var item in items)
                    {
                        if (string.IsNullOrEmpty(item.Category))
                            continue;
                            
                        if (!cachedCategories.ContainsKey(item.Category))
                        {
                            cachedCategories[item.Category] = new HashSet<string>();
                        }
                        
                        if (!string.IsNullOrEmpty(item.SubCategory))
                        {
                            cachedCategories[item.Category].Add(item.SubCategory);
                        }
                    }
                    
                    // Cache the result for future use
                    _cacheService.Set("CategoryHierarchy", cachedCategories, TimeSpan.FromMinutes(10));
                }
                
                // Clear existing categories
                Categories.Clear();
                
                // Add categories to collection
                foreach (var category in cachedCategories)
                {
                    var categoryViewModel = new CategoryViewModel(
                        category.Key, 
                        _dataContext,
                        _auditService,
                        _exceptionHandler);
                        
                    // Add subcategories
                    foreach (var subcategory in category.Value)
                    {
                        categoryViewModel.AddSubcategory(subcategory);
                    }
                    
                    Categories.Add(categoryViewModel);
                }
                
                // Set up filtering
                FilteredCategories = CollectionViewSource.GetDefaultView(Categories);
                FilteredCategories.Filter = CategoryFilter;
                
                // Log audit trail for viewing categories
                await _auditService.LogActionAsync(
                    "Categories", 
                    "View", 
                    $"Viewed {Categories.Count} categories", 
                    null);
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading categories");
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        private async Task LoadStatisticsAsync()
        {
            try
            {
                // Count total categories
                TotalCategories = Categories.Count;
                
                // Count total subcategories
                TotalSubcategories = Categories.Sum(c => c.Subcategories.Count);
                
                // Count items without category
                ItemsWithoutCategory = await _dataContext.Items
                    .Where(i => !i.IsDeleted && string.IsNullOrEmpty(i.Category))
                    .CountAsync();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, "Error loading category statistics");
            }
        }
        
        private bool CategoryFilter(object item)
        {
            if (item is not CategoryViewModel category)
                return false;
            
            // Apply search text filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                bool matchesCategory = category.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase);
                
                // Check subcategories as well
                bool matchesSubcategory = category.Subcategories.Any(sc => 
                    sc.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
                
                return matchesCategory || matchesSubcategory;
            }
            
            return true;
        }
        
        private void ApplyFilters()
        {
            FilteredCategories?.Refresh();
        }
        
        private void OpenAddCategoryDialog()
        {
            // Implementation would open a dialog for adding a new category
            // This would typically use a dialog service
        }
        
        #endregion
    }
    
    /// <summary>
    /// ViewModel for an individual category in the UI
    /// </summary>
    public class CategoryViewModel : ViewModelBase
    {
        private readonly string _name;
        private readonly IDataContext _dataContext;
        private readonly IAuditService _auditService;
        private readonly EnhancedExceptionHandler _exceptionHandler;
        private int _itemCount;
        
        public CategoryViewModel(
            string name,
            IDataContext dataContext,
            IAuditService auditService,
            EnhancedExceptionHandler exceptionHandler)
        {
            _name = name ?? throw new ArgumentNullException(nameof(name));
            _dataContext = dataContext ?? throw new ArgumentNullException(nameof(dataContext));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            
            // Initialize collections
            Subcategories = new ObservableCollection<string>();
            
            // Initialize commands
            EditCommand = new RelayCommand(param => EditCategory());
            DeleteCommand = new RelayCommand(param => DeleteCategory());
            
            // Load item count
            _ = LoadItemCountAsync();
        }
        
        #region Properties
        
        public string Name => _name;
        
        public ObservableCollection<string> Subcategories { get; }
        
        public int ItemCount
        {
            get => _itemCount;
            private set => SetProperty(ref _itemCount, value);
        }
        
        #endregion
        
        #region Commands
        
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }
        
        #endregion
        
        #region Methods
        
        public void AddSubcategory(string subcategory)
        {
            if (string.IsNullOrEmpty(subcategory))
                return;
                
            if (!Subcategories.Contains(subcategory))
            {
                Subcategories.Add(subcategory);
            }
        }
        
        private async Task LoadItemCountAsync()
        {
            try
            {
                // Count items in this category
                ItemCount = await _dataContext.Items
                    .Where(i => !i.IsDeleted && i.Category == _name)
                    .CountAsync();
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, $"Error loading item count for category {_name}");
            }
        }
        
        private void EditCategory()
        {
            // Implementation would open a dialog for editing this category
            // This would typically use a dialog service
        }
        
        private async void DeleteCategory()
        {
            try
            {
                // Check if there are items in this category
                if (ItemCount > 0)
                {
                    // Cannot delete category with items
                    return;
                }
                
                // In a real implementation, we would update items in the database
                // For this example, we just log the action
                await _auditService.LogActionAsync(
                    "Categories",
                    "Delete",
                    $"Deleted category {_name}",
                    _name);
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleExceptionAsync(ex, $"Error deleting category {_name}");
            }
        }
        
        #endregion
    }
}
