using System;

namespace InventoryManagement.Infrastructure.Configuration
{
    /// <summary>
    /// Main application settings
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// Application name
        /// </summary>
        public string ApplicationName { get; set; } = "Tom General Trading Inventory Management System";

        /// <summary>
        /// Company name
        /// </summary>
        public string CompanyName { get; set; } = "Tom General Trading";

        /// <summary>
        /// Application version
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// Whether the application is in debug mode
        /// </summary>
        public bool DebugMode { get; set; } = false;

        /// <summary>
        /// Security settings
        /// </summary>
        public SecuritySettings Security { get; set; } = new SecuritySettings();

        /// <summary>
        /// Database settings
        /// </summary>
        public DatabaseSettings Database { get; set; } = new DatabaseSettings();

        /// <summary>
        /// Offline mode settings
        /// </summary>
        public OfflineConfiguration Offline { get; set; } = new OfflineConfiguration();

        /// <summary>
        /// UI settings
        /// </summary>
        public UiSettings Ui { get; set; } = new UiSettings();
    }



    /// <summary>
    /// Database backup settings
    /// </summary>
    public class DatabaseBackupSettings
    {
        /// <summary>
        /// Whether to enable automatic backups
        /// </summary>
        public bool EnableAutoBackup { get; set; } = true;

        /// <summary>
        /// Backup interval in hours
        /// </summary>
        public int BackupIntervalHours { get; set; } = 24;

        /// <summary>
        /// Backup path
        /// </summary>
        public string BackupPath { get; set; } = "App_Data\\Backups";

        /// <summary>
        /// Maximum number of backups to keep
        /// </summary>
        public int MaxBackups { get; set; } = 10;

        /// <summary>
        /// Whether to compress backups
        /// </summary>
        public bool CompressBackups { get; set; } = true;
    }

    /// <summary>
    /// UI settings
    /// </summary>
    public class UiSettings
    {
        /// <summary>
        /// Theme
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// Primary color
        /// </summary>
        public string PrimaryColor { get; set; } = "#0078D7";

        /// <summary>
        /// Secondary color
        /// </summary>
        public string SecondaryColor { get; set; } = "#E81123";

        /// <summary>
        /// Font size
        /// </summary>
        public int FontSize { get; set; } = 12;

        /// <summary>
        /// Default language
        /// </summary>
        public string DefaultLanguage { get; set; } = "en-US";
    }
}
